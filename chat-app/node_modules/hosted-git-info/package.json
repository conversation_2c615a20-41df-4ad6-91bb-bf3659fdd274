{"name": "hosted-git-info", "version": "8.1.0", "description": "Provides metadata and conversions from repository urls for GitHub, Bitbucket and GitLab", "main": "./lib/index.js", "repository": {"type": "git", "url": "git+https://github.com/npm/hosted-git-info.git"}, "keywords": ["git", "github", "bitbucket", "gitlab"], "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/hosted-git-info/issues"}, "homepage": "https://github.com/npm/hosted-git-info", "scripts": {"posttest": "npm run lint", "snap": "tap", "test": "tap", "test:coverage": "tap --coverage-report=html", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "dependencies": {"lru-cache": "^10.0.1"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.24.3", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "tap": {"color": 1, "coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.24.3", "publish": "true"}}