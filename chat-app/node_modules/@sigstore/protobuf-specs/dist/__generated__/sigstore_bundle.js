"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: sigstore_bundle.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bundle = exports.VerificationMaterial = exports.TimestampVerificationData = void 0;
/* eslint-disable */
const envelope_1 = require("./envelope");
const sigstore_common_1 = require("./sigstore_common");
const sigstore_rekor_1 = require("./sigstore_rekor");
exports.TimestampVerificationData = {
    fromJSON(object) {
        return {
            rfc3161Timestamps: globalThis.Array.isArray(object?.rfc3161Timestamps)
                ? object.rfc3161Timestamps.map((e) => sigstore_common_1.RFC3161SignedTimestamp.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.rfc3161Timestamps?.length) {
            obj.rfc3161Timestamps = message.rfc3161Timestamps.map((e) => sigstore_common_1.RFC3161SignedTimestamp.toJSON(e));
        }
        return obj;
    },
};
exports.VerificationMaterial = {
    fromJSON(object) {
        return {
            content: isSet(object.publicKey)
                ? { $case: "publicKey", publicKey: sigstore_common_1.PublicKeyIdentifier.fromJSON(object.publicKey) }
                : isSet(object.x509CertificateChain)
                    ? {
                        $case: "x509CertificateChain",
                        x509CertificateChain: sigstore_common_1.X509CertificateChain.fromJSON(object.x509CertificateChain),
                    }
                    : isSet(object.certificate)
                        ? { $case: "certificate", certificate: sigstore_common_1.X509Certificate.fromJSON(object.certificate) }
                        : undefined,
            tlogEntries: globalThis.Array.isArray(object?.tlogEntries)
                ? object.tlogEntries.map((e) => sigstore_rekor_1.TransparencyLogEntry.fromJSON(e))
                : [],
            timestampVerificationData: isSet(object.timestampVerificationData)
                ? exports.TimestampVerificationData.fromJSON(object.timestampVerificationData)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.content?.$case === "publicKey") {
            obj.publicKey = sigstore_common_1.PublicKeyIdentifier.toJSON(message.content.publicKey);
        }
        else if (message.content?.$case === "x509CertificateChain") {
            obj.x509CertificateChain = sigstore_common_1.X509CertificateChain.toJSON(message.content.x509CertificateChain);
        }
        else if (message.content?.$case === "certificate") {
            obj.certificate = sigstore_common_1.X509Certificate.toJSON(message.content.certificate);
        }
        if (message.tlogEntries?.length) {
            obj.tlogEntries = message.tlogEntries.map((e) => sigstore_rekor_1.TransparencyLogEntry.toJSON(e));
        }
        if (message.timestampVerificationData !== undefined) {
            obj.timestampVerificationData = exports.TimestampVerificationData.toJSON(message.timestampVerificationData);
        }
        return obj;
    },
};
exports.Bundle = {
    fromJSON(object) {
        return {
            mediaType: isSet(object.mediaType) ? globalThis.String(object.mediaType) : "",
            verificationMaterial: isSet(object.verificationMaterial)
                ? exports.VerificationMaterial.fromJSON(object.verificationMaterial)
                : undefined,
            content: isSet(object.messageSignature)
                ? { $case: "messageSignature", messageSignature: sigstore_common_1.MessageSignature.fromJSON(object.messageSignature) }
                : isSet(object.dsseEnvelope)
                    ? { $case: "dsseEnvelope", dsseEnvelope: envelope_1.Envelope.fromJSON(object.dsseEnvelope) }
                    : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.mediaType !== "") {
            obj.mediaType = message.mediaType;
        }
        if (message.verificationMaterial !== undefined) {
            obj.verificationMaterial = exports.VerificationMaterial.toJSON(message.verificationMaterial);
        }
        if (message.content?.$case === "messageSignature") {
            obj.messageSignature = sigstore_common_1.MessageSignature.toJSON(message.content.messageSignature);
        }
        else if (message.content?.$case === "dsseEnvelope") {
            obj.dsseEnvelope = envelope_1.Envelope.toJSON(message.content.dsseEnvelope);
        }
        return obj;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
