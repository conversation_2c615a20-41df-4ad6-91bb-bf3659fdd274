import * as crypto from '../crypto';
interface SCTOptions {
    version: number;
    logID: Buffer;
    timestamp: <PERSON>uffer;
    extensions: <PERSON>uffer;
    hashAlgorithm: number;
    signatureAlgorithm: number;
    signature: <PERSON><PERSON><PERSON>;
}
export declare class SignedCertificateTimestamp {
    readonly version: number;
    readonly logID: Buffer;
    readonly timestamp: Buffer;
    readonly extensions: <PERSON>uffer;
    readonly hashAlgorithm: number;
    readonly signatureAlgorithm: number;
    readonly signature: Buffer;
    constructor(options: SCTOptions);
    get datetime(): Date;
    get algorithm(): string;
    verify(preCert: Buffer, key: crypto.KeyObject): boolean;
    static parse(buf: Buffer): SignedCertificateTimestamp;
}
export {};
