{"name": "@npmcli/redact", "version": "3.2.2", "description": "Redact sensitive npm information from output", "main": "lib/index.js", "exports": {".": "./lib/index.js", "./server": "./lib/server.js", "./package.json": "./package.json"}, "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "keywords": [], "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "repository": {"type": "git", "url": "git+https://github.com/npm/redact.git"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.24.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 120}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.24.3", "tap": "^16.3.10"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}