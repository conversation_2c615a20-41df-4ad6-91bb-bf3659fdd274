"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeSeparator = exports.RoutingScope = void 0;
/**
 * The scope for the new routing module.
 */
var RoutingScope;
(function (RoutingScope) {
    RoutingScope["Child"] = "Child";
    RoutingScope["Root"] = "Root";
})(RoutingScope || (exports.RoutingScope = RoutingScope = {}));
/**
 * The separator character to use before the type within the generated file's name. For
 * example, if you set the option to `.`, the file will be named `example.module.ts`.
 */
var TypeSeparator;
(function (TypeSeparator) {
    TypeSeparator["Empty"] = "-";
    TypeSeparator["TypeSeparator"] = ".";
})(TypeSeparator || (exports.TypeSeparator = TypeSeparator = {}));
