/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["bas", [["I bikɛ̂glà", "I ɓugajɔp"], u, u], u, [["n", "n", "u", "ŋ", "m", "k", "j"], ["nɔy", "nja", "uum", "ŋge", "mbɔ", "kɔɔ", "jon"], ["ŋgwà nɔ̂y", "ŋgwà njaŋgumba", "ŋgwà ûm", "ŋgwà ŋgê", "ŋgwà mbɔk", "ŋgwà kɔɔ", "ŋgwà jôn"], ["nɔy", "nja", "uum", "ŋge", "mbɔ", "kɔɔ", "jon"]], u, [["k", "m", "m", "m", "m", "h", "n", "h", "d", "b", "m", "l"], ["kɔn", "mac", "mat", "mto", "mpu", "hil", "nje", "hik", "dip", "bio", "may", "liɓ"], ["Kɔndɔŋ", "Màcɛ̂l", "Màtùmb", "Màtop", "M̀puyɛ", "Hìlòndɛ̀", "Njèbà", "Hìkaŋ", "Dìpɔ̀s", "Bìòôm", "Màyɛsèp", "Lìbuy li ńyèe"]], u, [["b.Y.K", "m.Y.K"], u, ["bisū bi Yesù Krǐstò", "i mbūs Yesù Krǐstò"]], 1, [6, 0], ["d/M/y", "d MMM, y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "XAF", "FCFA", "Frǎŋ CFA (BEAC)", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=bas.js.map