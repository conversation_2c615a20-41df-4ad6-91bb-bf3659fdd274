/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["as", [["পূৰ্বাহ্ন", "অপৰাহ্ন"], u, u], u, [["দ", "স", "ম", "ব", "ব", "শ", "শ"], ["দেও", "সোম", "মঙ্গল", "বুধ", "বৃহ", "শুক্ৰ", "শনি"], ["দেওবাৰ", "সোমবাৰ", "মঙ্গলবাৰ", "বুধবাৰ", "বৃহস্পতিবাৰ", "শুক্ৰবাৰ", "শনিবাৰ"], ["দেও", "সোম", "মঙ্গল", "বুধ", "বৃহ", "শুক্ৰ", "শনি"]], u, [["জ", "ফ", "ম", "এ", "ম", "জ", "জ", "আ", "ছ", "অ", "ন", "ড"], ["জানু", "ফেব্ৰু", "মাৰ্চ", "এপ্ৰিল", "মে’", "জুন", "জুলাই", "আগ", "ছেপ্তে", "অক্টো", "নৱে", "ডিচে"], ["জানুৱাৰী", "ফেব্ৰুৱাৰী", "মাৰ্চ", "এপ্ৰিল", "মে’", "জুন", "জুলাই", "আগষ্ট", "ছেপ্তেম্বৰ", "অক্টোবৰ", "নৱেম্বৰ", "ডিচেম্বৰ"]], u, [["খ্ৰীঃ পূঃ", "খ্ৰীঃ"], u, ["খ্ৰীষ্টপূৰ্ব", "খ্ৰীষ্টাব্দ"]], 0, [0, 0], ["d-M-y", "dd-MM-y", "d MMMM, y", "EEEE, d MMMM, y"], ["a h.mm", "a h.mm.ss", "a h.mm.ss z", "a h.mm.ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤ #,##,##0.00", "#E0"], "INR", "₹", "ভাৰতীয় ৰুপী", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=as.js.map