from langchain.llms.base import LLM
from langchain.schema import Generation, LLMResult
import requests
from openai import OpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter



class chat_dongo(LLM):
    """chat_dongo est un agent qui permet de prendre en charge et un document et de faciliter la compréhension en posant des questions."""

    def __init__(self):
        self.client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")
        self.embdeding_model = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")
        self.document = None
        self.chunks = None
        

# indexation : elle consiste à dans un premier temps à charger le doc, le decouper et ensuite l'encode oi le vectorise pour le stocker dans un vectorestore

    # création d'une fonction qui permettra de charger un document
    def load_document(self, document_path):
        """Charge un document et le stocke dans une variable."""
        with open(document_path, "r") as f:
             self.document = f.read()
        return self.document

    #fonction pour traiter l'entrée utilisateur avec le model

    def read_input(self, user_input):
        """Lit l'entrée utilisateur et la traite avec le modèle."""
        response = self.client.chat.completions.create(
            model="deepseek/deepseek-r1-0528-qwen3-8b",
            messages=[
                {"role": "user", "content": user_input}
            ]
        )
        return response.choices[0].message.content

    #split the document readed avec langchain 
    # on lui passe le document charger avec la methode load_document
    
    def split_document(self):
        """Split the document into chunks."""
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        self.chunks = text_splitter.split_text(self.document)

    #notre model a son url pour faire l'embedding
    #on va utiliser la librairie langchain pour faire l'embedding avec le model local
    
    def embdeding_chunks(self):
        """Embed the chunks."""
        embeddings = self.embdeding_model.embeddings.create(
            model="deepseek/deepseek-r1-0528-qwen3-8b",
            input=self.chunks
        )
        return embeddings
    
    

    def embedding_user_input(self, user_input):
        """Embed the user input."""
        embeddings = self.embdeding_model.embeddings.embed_query(user_input)
        return embeddings
    
    def create_vectorstore(self):
        """Crée un vector store avec les chunks embeddings."""
        if not self.chunks:
            raise ValueError("Aucun chunk disponible. Veuillez d'abord charger et diviser le document.")

        # Créer les embeddings pour tous les chunks
        embeddings_data = []
        for i, chunk in enumerate(self.chunks):
            # Utiliser votre modèle local pour créer l'embedding
            embedding_response = self.embdeding_model.embeddings.create(
                model="deepseek/deepseek-r1-0528-qwen3-8b",
                input=[chunk]
            )
            # Extraire le vecteur d'embedding
            embedding_vector = embedding_response.data[0].embedding

            embeddings_data.append({
                'id': f"chunk_{i}",
                'text': chunk,
                'embedding': embedding_vector
            })

        # Stocker dans une structure simple (vous pouvez utiliser une base de données plus tard)
        self.vector_store = embeddings_data
        return self.vector_store

    def search_similar_chunks(self, user_query, top_k=3):
        """Recherche les chunks les plus similaires à la requête utilisateur."""
        if not hasattr(self, 'vector_store') or not self.vector_store:
            raise ValueError("Vector store non initialisé. Veuillez d'abord créer le vector store.")

        # Créer l'embedding de la requête utilisateur
        query_embedding_response = self.embdeding_model.embeddings.create(
            model="deepseek/deepseek-r1-0528-qwen3-8b",
            input=[user_query]
        )
        query_embedding = query_embedding_response.data[0].embedding

        # Calculer la similarité cosinus avec tous les chunks
        similarities = []
        for item in self.vector_store:
            similarity = self._cosine_similarity(query_embedding, item['embedding'])
            similarities.append({
                'text': item['text'],
                'similarity': similarity,
                'id': item['id']
            })

        # Trier par similarité décroissante et retourner les top_k
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return similarities[:top_k]

    def _cosine_similarity(self, vec1, vec2):
        """Calcule la similarité cosinus entre deux vecteurs."""
        import math

        # Produit scalaire
        dot_product = sum(a * b for a, b in zip(vec1, vec2))

        # Normes
        norm1 = math.sqrt(sum(a * a for a in vec1))
        norm2 = math.sqrt(sum(a * a for a in vec2))

        # Similarité cosinus
        if norm1 == 0 or norm2 == 0:
            return 0
        return dot_product / (norm1 * norm2)

    def rag_query(self, user_question, top_k=3):
        """Effectue une requête RAG complète : recherche + génération."""
        # Rechercher les chunks les plus pertinents
        relevant_chunks = self.search_similar_chunks(user_question, top_k)

        # Construire le contexte à partir des chunks pertinents
        context = "\n\n".join([chunk['text'] for chunk in relevant_chunks])

        # Créer le prompt avec le contexte
        prompt = f"""Contexte:
{context}

Question: {user_question}

Répondez à la question en vous basant sur le contexte fourni. Si la réponse n'est pas dans le contexte, dites-le clairement."""

        # Générer la réponse avec le modèle
        response = self.client.chat.completions.create(
            model="deepseek/deepseek-r1-0528-qwen3-8b",
            messages=[
                {"role": "user", "content": prompt}
            ]
        )

        return {
            'answer': response.choices[0].message.content,
            'relevant_chunks': relevant_chunks,
            'context_used': context
        }