from langchain.llms.base import LLM
from langchain.schema import Generation, LLMResult
import requests
from openai import OpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import LMDBVectorStore
from langchain_core.vectorstores import InMemoryVectorStore


class chat_dongo(LLM):
    """chat_dongo est un agent qui permet de prendre en charge et un document et de faciliter la compréhension en posant des questions."""

    def __init__(self):
        self.client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")
        self.embdeding_model = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")
        self.document = None
        self.chunks = None
        

# indexation : elle consiste à dans un premier temps à charger le doc, le decouper et ensuite l'encode oi le vectorise pour le stocker dans un vectorestore

    # création d'une fonction qui permettra de charger un document
    def load_document(self, document_path):
        """Charge un document et le stocke dans une variable."""
        with open(document_path, "r") as f:
             self.document = f.read()
        return self.document

    #fonction pour traiter l'entrée utilisateur avec le model

    def read_input(self, user_input):
        """Lit l'entrée utilisateur et la traite avec le modèle."""
        response = self.client.chat.completions.create(
            model="deepseek/deepseek-r1-0528-qwen3-8b",
            messages=[
                {"role": "user", "content": user_input}
            ]
        )
        return response.choices[0].message.content

    #split the document readed avec langchain 
    # on lui passe le document charger avec la methode load_document
    
    def split_document(self):
        """Split the document into chunks."""
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        self.chunks = text_splitter.split_text(self.document)

    #notre model a son url pour faire l'embedding
    #on va utiliser la librairie langchain pour faire l'embedding avec le model local
    
    def embdeding_chunks(self):
        """Embed the chunks."""
        embeddings = self.embdeding_model.embeddings.create(
            model="deepseek/deepseek-r1-0528-qwen3-8b",
            input=self.chunks
        )
        return embeddings
    
    

    def embedding_user_input(self, user_input):
        """Embed the user input."""
        embeddings = self.embdeding_model.embeddings.embed_query(user_input)
        return embeddings
    
    def vectorstore(self):
        
        # Initialize with an embedding model
        vector_store = InMemoryVectorStore(embedding=SomeEmbeddingModel())



    